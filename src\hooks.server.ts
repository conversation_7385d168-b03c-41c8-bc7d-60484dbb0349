import { adminAuth } from "$lib/server/admin.ts";
import { redirect, type Handle, type ServerInit } from "@sveltejs/kit";
import { adminDB } from "$lib/server/admin.ts";
import type { UserRecord } from "firebase-admin/auth";
import type { DecodedIdToken } from "firebase-admin/auth";
import type { HandleServerError } from '@sveltejs/kit';
import { PostHog } from 'posthog-node';
import { dev } from "$app/environment";

// Map to store last update timestamps for each user
const lastUpdateTimestamps = new Map<string, number>();
const DEBOUNCE_TIME = 2000; // 2 seconds debounce time

let unsubscribeListener: (() => void) | null = null;

export const init: ServerInit = async () => {
    // Set up a listener for user role changes in Firestore
    unsubscribeListener = adminDB.collection('users').onSnapshot(
        async (snapshot) => {
            const changes = snapshot.docChanges();
            
            for (const change of changes) {
                if (change.type === 'modified') {
                    const userData = change.doc.data();
                    const userId = change.doc.id;
                    
                    // Check if we should process this update (debouncing)
                    const now = Date.now();
                    const lastUpdate = lastUpdateTimestamps.get(userId) || 0;
                    if (now - lastUpdate < DEBOUNCE_TIME) {
                        console.log(`Skipping rapid update for user ${userId}`);
                        continue;
                    }
                    
                    try {
                        // Get current user to check existing claims
                        const user = await adminAuth.getUser(userId);
                        const currentRole = user.customClaims?.role;
                        
                        // Only update if the role has changed
                        if (currentRole !== userData.role) {                            
                            await adminAuth.setCustomUserClaims(userId, { 
                                role: userData.role 
                            });
                            
                            // Update the timestamp
                            lastUpdateTimestamps.set(userId, now);
                            
                            console.log(`Successfully updated custom claims for user ${userId}: ${currentRole} --> ${userData.role}`);
                        } else {
                            console.log(`No role change needed for user ${userId}, current role: ${currentRole}`);
                        }
                    } catch (error) {
                        console.error(`Error processing role update for user ${userId}:`, error);
                    }
                }
            }
        },
        (error) => {
            console.error('Error in Firestore listener:', error);
        }
    );
};

// Cleanup function to be called when the server shuts down
process.on('SIGTERM', () => {
    if (unsubscribeListener) {
        unsubscribeListener();
        lastUpdateTimestamps.clear();
    }
});

/**
 * Server-side hook that handles authentication and authorization for the application.
 * This hook runs on every request and manages access control for study-related routes.
 */
export const handle: Handle = async ({ event, resolve }) => {
    const sessionCookie = event.cookies.get("__session");
    const isStudyPath = event.url.pathname.startsWith("/study");
    const isBootcampPath = event.url.pathname.startsWith("/bootcamp");

    // Require the user to sign up to access the study path
    if (!sessionCookie && isStudyPath && isBootcampPath) {
        redirectTo(event, "/sign-up");
    }

    let decodedClaims: DecodedIdToken | null = null;
    let user: UserRecord | null = null;

    if (sessionCookie) {
        // Verify the session cookie and get user's custom claims
        decodedClaims = await adminAuth.verifySessionCookie(sessionCookie);
        user = await adminAuth.getUser(decodedClaims.uid);
    }    

    // Store user information in event.locals for use in the application
    event.locals.uid = decodedClaims?.uid || null;
    event.locals.role = user?.customClaims.role || null;

    if (isBootcampPath && user.customClaims.role !== "Bootcamp" && user.customClaims.role !== "Pro") {
        redirectTo(event, "/simulation");
    }

    return resolve(event);
}

/**
 * Redirects to the sign-up page with the current URL as the redirectTo parameter.
 * This allows users to return to their intended destination after signing up.
 * @param event - The event object containing the current request information
 */
const redirectTo = (event: any, path: string) => {
  const fromUrl = "/" + (event.url.pathname + event.url.search).slice(1);
  redirect(302, `${path}?redirectTo=${fromUrl}`);
};


const client = new PostHog(
    'phc_XqrqMXQs6uFmKbnA9wPoAMe2gDKQqAXlVoOFXQBmNS3',
    { host: 'https://eu.i.posthog.com' }
)

export const handleError: HandleServerError = async ({ error, status }) => {
    if (status !== 404 && !dev) {
        client.captureException(error);
        await client.shutdown();
    }

    if (dev) console.error(error);
};