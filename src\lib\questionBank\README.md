# Question Bank System

Individual question practice interface providing targeted skill development with filtering, progress tracking, and detailed explanations.

## Overview

The question bank system allows students to practice individual questions outside of full mock tests. It features intelligent filtering, progress tracking, detailed explanations, and integration with the missions system for daily practice goals.

## Architecture

```
questionBank/
└── QuestionBank.svelte    # Main question bank interface
```

## Core Features

### Question Practice Interface
```svelte
<!-- QuestionBank.svelte - Main component -->
<script>
  import { PracticeQuestion, QuestionHeader } from '$lib/ui';
  import { incrementMissionProgress } from '$lib/missions';
  import { supabase } from '$lib/server';
  
  let currentQuestion = $state(null);
  let userAnswer = $state('');
  let showExplanation = $state(false);
  let questionsAnswered = $state(0);
  
  // Filtering state
  let selectedDifficulty = $state('all');
  let selectedType = $state('all');
  let selectedModule = $state('all');
  
  // Progress tracking
  let totalCorrect = $state(0);
  let streakCount = $state(0);
  
  async function loadNextQuestion() {
    const filters = {
      difficulty: selectedDifficulty !== 'all' ? selectedDifficulty : null,
      questionType: selectedType !== 'all' ? selectedType : null,
      module: selectedModule !== 'all' ? selectedModule : null
    };
    
    currentQuestion = await fetchFilteredQuestion(filters);
    userAnswer = '';
    showExplanation = false;
  }
  
  async function submitAnswer() {
    const isCorrect = userAnswer === currentQuestion.correct_answer;
    
    if (isCorrect) {
      totalCorrect++;
      streakCount++;
    } else {
      streakCount = 0;
    }
    
    // Update mission progress
    await incrementMissionProgress($user?.uid, 'questions_answered', 1);
    
    // Track performance
    await saveQuestionResult({
      questionId: currentQuestion.id,
      userAnswer,
      isCorrect,
      timeSpent: timeSpent
    });
    
    showExplanation = true;
    questionsAnswered++;
  }
</script>

<div class="question-bank">
  <div class="filters">
    <select bind:value={selectedDifficulty} onchange={loadNextQuestion}>
      <option value="all">All Difficulties</option>
      <option value="1">Easy</option>
      <option value="2">Medium</option>
      <option value="3">Hard</option>
    </select>
    
    <select bind:value={selectedType} onchange={loadNextQuestion}>
      <option value="all">All Types</option>
      <option value="Reading and Writing">Reading & Writing</option>
      <option value="Math">Math</option>
    </select>
    
    <select bind:value={selectedModule} onchange={loadNextQuestion}>
      <option value="all">All Modules</option>
      <option value="Algebra">Algebra</option>
      <option value="Geometry">Geometry</option>
      <option value="Reading">Reading Comprehension</option>
      <option value="Writing">Grammar & Usage</option>
    </select>
  </div>
  
  {#if currentQuestion}
    <QuestionHeader 
      difficulty={currentQuestion.difficulty}
      type={currentQuestion.question_type}
      skill={currentQuestion.skill}
      number={questionsAnswered + 1}
    />
    
    <PracticeQuestion 
      question={currentQuestion}
      {userAnswer}
      onAnswerChange={(answer) => userAnswer = answer}
      onSubmit={submitAnswer}
      {showExplanation}
      disabled={showExplanation}
    />
    
    {#if showExplanation}
      <div class="explanation-panel">
        <div class="result" class:correct={userAnswer === currentQuestion.correct_answer}>
          {userAnswer === currentQuestion.correct_answer ? '✅ Correct!' : '❌ Incorrect'}
        </div>
        
        <div class="explanation">
          <h4>Explanation:</h4>
          <p>{@html currentQuestion.explanation}</p>
        </div>
        
        <button onclick={loadNextQuestion}>
          Next Question
        </button>
      </div>
    {/if}
  {/if}
</div>
```

## Filtering System

### Advanced Question Filtering
```typescript
interface QuestionFilters {
  difficulty?: number;        // 1-5 difficulty level
  questionType?: string;      // 'Reading and Writing' | 'Math'
  module?: string;           // Subject area (Algebra, Geometry, etc.)
  skill?: string;            // Specific skill (Linear Equations, etc.)
  hasPassage?: boolean;      // Questions with reading passages
  answered?: boolean;        // Previously answered questions
  correct?: boolean;         // Previously answered correctly
}

async function fetchFilteredQuestion(filters: QuestionFilters) {
  let query = supabase
    .from('questions')
    .select('*');
  
  if (filters.difficulty) {
    query = query.eq('difficulty', filters.difficulty);
  }
  
  if (filters.questionType) {
    query = query.eq('question_type', filters.questionType);
  }
  
  if (filters.module) {
    query = query.eq('module', filters.module);
  }
  
  // Exclude already answered questions (optional)
  if (filters.answered === false) {
    const answeredQuestions = await getUserAnsweredQuestions(userId);
    query = query.not('id', 'in', `(${answeredQuestions.join(',')})`);
  }
  
  const { data, error } = await query
    .order('random()')  // Get random question
    .limit(1)
    .single();
    
  return data;
}
```

### Smart Question Selection
```typescript
// Adaptive question selection based on performance
class QuestionSelector {
  private userPerformance: Map<string, number> = new Map();
  
  async selectNextQuestion(userId: string): Promise<Question> {
    const weakAreas = await this.identifyWeakAreas(userId);
    
    // Prioritize weak areas (70% of the time)
    if (Math.random() < 0.7 && weakAreas.length > 0) {
      return await this.getQuestionFromWeakArea(weakAreas[0]);
    }
    
    // Otherwise, select balanced question
    return await this.getBalancedQuestion();
  }
  
  private async identifyWeakAreas(userId: string): Promise<string[]> {
    const performance = await getUserPerformanceBySkill(userId);
    
    return Object.entries(performance)
      .filter(([skill, accuracy]) => accuracy < 0.6) // Less than 60% accuracy
      .map(([skill]) => skill)
      .sort(); // Sort by performance (worst first)
  }
}
```

## Progress Tracking

### Performance Analytics
```typescript
interface QuestionResult {
  questionId: string;
  userId: string;
  userAnswer: string;
  correctAnswer: string;
  isCorrect: boolean;
  timeSpent: number;         // Seconds spent on question
  difficulty: number;
  skill: string;
  attemptedAt: Date;
}

// Save question result
async function saveQuestionResult(result: QuestionResult) {
  // Save to Firestore for user analytics
  await setDoc(doc(db, 'users', result.userId, 'questionResults', result.questionId), {
    ...result,
    attemptedAt: new Date()
  });
  
  // Update user performance metrics
  await updateUserPerformance(result.userId, result);
}

// Update aggregated performance data
async function updateUserPerformance(userId: string, result: QuestionResult) {
  const userRef = doc(db, 'users', userId);
  
  await runTransaction(db, async (transaction) => {
    const userDoc = await transaction.get(userRef);
    const userData = userDoc.data();
    
    // Update question bank progress
    const qbProgress = userData.questionBankProgress || {
      totalAnswered: 0,
      totalCorrect: 0,
      bySkill: {},
      byDifficulty: {}
    };
    
    qbProgress.totalAnswered++;
    if (result.isCorrect) qbProgress.totalCorrect++;
    
    // Update by skill
    const skillKey = result.skill;
    qbProgress.bySkill[skillKey] = qbProgress.bySkill[skillKey] || { answered: 0, correct: 0 };
    qbProgress.bySkill[skillKey].answered++;
    if (result.isCorrect) qbProgress.bySkill[skillKey].correct++;
    
    // Update by difficulty
    const diffKey = `difficulty_${result.difficulty}`;
    qbProgress.byDifficulty[diffKey] = qbProgress.byDifficulty[diffKey] || { answered: 0, correct: 0 };
    qbProgress.byDifficulty[diffKey].answered++;
    if (result.isCorrect) qbProgress.byDifficulty[diffKey].correct++;
    
    transaction.update(userRef, { questionBankProgress: qbProgress });
  });
}
```

### Progress Visualization
```svelte
<!-- Progress display within QuestionBank -->
<script>
  import { CircularProgressBar } from '$lib/analysis';
  
  let userProgress = $derived(calculateProgress(totalAnswered, totalCorrect, streakCount));
  
  function calculateProgress(answered, correct, streak) {
    return {
      accuracy: answered > 0 ? Math.round((correct / answered) * 100) : 0,
      questionsToday: answered,
      currentStreak: streak,
      totalAnswered: answered
    };
  }
</script>

<div class="progress-panel">
  <div class="stat-card">
    <h4>Today's Progress</h4>
    <p>{userProgress.questionsToday} questions answered</p>
    <CircularProgressBar 
      current={userProgress.questionsToday}
      target={5}
      label="Daily Goal"
    />
  </div>
  
  <div class="stat-card">
    <h4>Accuracy</h4>
    <p>{userProgress.accuracy}%</p>
  </div>
  
  <div class="stat-card">
    <h4>Current Streak</h4>
    <p>{userProgress.currentStreak} correct in a row</p>
  </div>
</div>
```

## Mission Integration

### Daily Question Goals
```typescript
// Integration with missions system
import { incrementMissionProgress } from '$lib/missions';

// When user answers a question
const handleQuestionAnswer = async (isCorrect: boolean) => {
  // Always increment questions answered
  await incrementMissionProgress($user?.uid, 'questions_answered', 1);
  
  // Track accuracy for mission completion
  if (isCorrect) {
    await incrementMissionProgress($user?.uid, 'questions_correct', 1);
  }
  
  // Check if daily goal reached
  const progress = await getMissionProgress($user?.uid, 'daily_questions');
  if (progress >= 5) {
    // Show completion message
    showMissionComplete('Daily Questions');
  }
};
```

## Question Types Support

### Multiple Choice Questions
```svelte
<!-- Multiple choice interface -->
<script>
  let { choices, selectedAnswer, onAnswerSelect } = $props();
  let letters = ['A', 'B', 'C', 'D'];
</script>

<div class="choices">
  {#each choices as choice, index}
    <label class="choice-option">
      <input 
        type="radio"
        name="answer"
        value={letters[index]}
        checked={selectedAnswer === letters[index]}
        onchange={() => onAnswerSelect(letters[index])}
      />
      <span class="choice-letter">{letters[index]}</span>
      <span class="choice-text">{@html choice}</span>
    </label>
  {/each}
</div>
```

### Grid-in Questions (Math)
```svelte
<!-- Student-Produced Response interface -->
<script>
  import { AnswerBox } from '$lib/mockTest/ui';
  
  let { answer, onAnswerChange } = $props();
  
  function validateGridIn(value: string): boolean {
    // Only allow numbers, decimal points, and fractions
    return /^[\d\.\/\-]+$/.test(value);
  }
</script>

<div class="grid-in-question">
  <p>Enter your answer in the box below:</p>
  
  <AnswerBox 
    value={answer}
    onChange={onAnswerChange}
    type="numeric"
    validator={validateGridIn}
    placeholder="Enter answer"
  />
  
  <div class="grid-in-help">
    <p>Tips:</p>
    <ul>
      <li>Enter fractions as 1/2 or decimals as 0.5</li>
      <li>Don't include units or commas</li>
      <li>Round to 3 decimal places if needed</li>
    </ul>
  </div>
</div>
```

## Study Recommendations

### Skill-based Suggestions
```typescript
// Generate personalized study recommendations
function generateStudyRecommendations(userProgress: UserProgress): StudyRecommendation[] {
  const recommendations: StudyRecommendation[] = [];
  
  // Identify weak skills (< 60% accuracy)
  const weakSkills = Object.entries(userProgress.bySkill)
    .filter(([skill, data]) => data.correct / data.answered < 0.6)
    .sort((a, b) => (a[1].correct / a[1].answered) - (b[1].correct / b[1].answered));
  
  weakSkills.forEach(([skill, data]) => {
    recommendations.push({
      skill,
      priority: 'high',
      accuracy: Math.round((data.correct / data.answered) * 100),
      questionsNeeded: Math.max(10 - data.answered, 5),
      resources: [
        `/bootcamp/notes/${getSkillNoteSlug(skill)}`,
        `/study/question-bank?skill=${skill}&difficulty=1`
      ]
    });
  });
  
  return recommendations;
}
```

## Development Tips

1. **Question Loading**: Implement question preloading for smooth transitions
2. **Local Storage**: Cache user preferences and filter selections
3. **Accessibility**: Ensure proper ARIA labels for question navigation
4. **Mobile UX**: Optimize touch interactions for mobile users
5. **Analytics**: Track time spent per question type for insights
6. **Error Handling**: Graceful fallback when questions fail to load
7. **Performance**: Lazy load explanations and additional content