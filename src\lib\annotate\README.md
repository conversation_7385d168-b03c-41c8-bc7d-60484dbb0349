# Annotation System

Question annotation system allowing students to highlight text and take notes during mock tests, similar to paper-based test experience.

## Overview

The annotation system provides highlighting and note-taking functionality for passage-based questions during SAT simulations. It maintains annotation state during test sessions and can persist data for review.

## Architecture

```
annotate/
└── annotateManager.ts    # Core annotation engine
```

## Core Features

- **Text Highlighting** - Select and highlight text passages
- **Note Taking** - Add contextual notes to highlighted sections
- **Session Persistence** - Maintain annotations during test sessions
- **Multiple Colors** - Different highlight colors for categorization
- **Undo/Redo** - Annotation history management

## Usage Examples

### Basic Annotation Setup
```typescript
import { annotateManager } from '$lib/annotate';

// Initialize annotation system for a passage
const annotations = annotateManager.init(passageId, userId);

// Create highlight
const highlight = annotateManager.createHighlight({
  text: "selected text",
  startOffset: 10,
  endOffset: 25,
  color: "yellow"
});

// Add note to highlight
annotateManager.addNote(highlight.id, "This is important for question 3");
```

### Integration with Mock Test
```svelte
<!-- In passage display component -->
<script>
  import { annotateManager } from '$lib/annotate';
  
  let passageElement;
  let annotations = $state([]);
  
  function handleTextSelection(event) {
    const selection = window.getSelection();
    if (selection.rangeCount > 0 && !selection.isCollapsed) {
      const highlight = annotateManager.createHighlight({
        range: selection.getRangeAt(0),
        color: selectedColor
      });
      annotations.push(highlight);
    }
  }
  
  function handleHighlightClick(highlightId) {
    // Show note editor or existing note
    annotateManager.editNote(highlightId);
  }
</script>

<div 
  bind:this={passageElement}
  on:mouseup={handleTextSelection}
  class="passage-text"
>
  {#each annotations as annotation}
    <span 
      class="highlight highlight-{annotation.color}"
      on:click={() => handleHighlightClick(annotation.id)}
    >
      {annotation.text}
    </span>
  {/each}
</div>
```

## Data Structure

### Annotation Object
```typescript
interface Annotation {
  id: string;                    // Unique identifier
  passageId: string;            // Associated passage
  userId: string;               // User who created annotation
  text: string;                 // Highlighted text
  startOffset: number;          // Start position in passage
  endOffset: number;            // End position in passage
  color: 'yellow' | 'blue' | 'green' | 'pink'; // Highlight color
  note?: string;                // Optional user note
  createdAt: Date;              // Creation timestamp
  updatedAt: Date;              // Last modification
}
```

### Annotation Manager API
```typescript
interface AnnotateManager {
  // Initialize annotations for passage
  init(passageId: string, userId: string): Annotation[];
  
  // Create new highlight
  createHighlight(options: HighlightOptions): Annotation;
  
  // Add or update note
  addNote(annotationId: string, note: string): void;
  
  // Remove annotation
  removeAnnotation(annotationId: string): void;
  
  // Clear all annotations
  clearAll(passageId: string): void;
  
  // Export annotations
  export(format: 'json' | 'text'): string;
  
  // Get annotations by passage
  getByPassage(passageId: string): Annotation[];
}
```

## Integration Points

### With Mock Test UI Components
```svelte
<!-- In MidVerbal.svelte or Passage.svelte -->
<script>
  import { annotateManager } from '$lib/annotate';
  import { Annotate } from '$lib/mockTest/simulation';
  
  let currentPassage = $props();
  let annotations = annotateManager.getByPassage(currentPassage.id);
</script>

<Annotate passageId={currentPassage.id} bind:annotations />
```

### With Test Session State
```typescript
// Store annotations in test session data
import { finalAnswers } from '$lib/stores';

// Save annotations with test results
const testSession = {
  answers: $finalAnswers,
  annotations: annotateManager.export('json'),
  completedAt: new Date()
};
```

### With Firebase Storage (Optional)
```typescript
// Persist annotations for review
import { db } from '$lib/firebase';
import { doc, setDoc } from 'firebase/firestore';

const saveAnnotations = async (userId: string, testId: string) => {
  const annotations = annotateManager.export('json');
  
  await setDoc(doc(db, 'testAnnotations', `${userId}_${testId}`), {
    userId,
    testId,
    annotations: JSON.parse(annotations),
    createdAt: new Date()
  });
};
```

## Styling

### CSS Classes
```css
.highlight {
  @apply cursor-pointer transition-all duration-200;
}

.highlight-yellow {
  @apply bg-yellow-200 hover:bg-yellow-300;
}

.highlight-blue {
  @apply bg-blue-200 hover:bg-blue-300;
}

.highlight-green {
  @apply bg-green-200 hover:bg-green-300;
}

.highlight-pink {
  @apply bg-pink-200 hover:bg-pink-300;
}

.annotation-note {
  @apply absolute bg-white border border-gray-300 rounded-lg p-2 shadow-lg;
  @apply text-sm max-w-xs z-50;
}
```

## Event Handling

### Text Selection
```typescript
// Handle text selection for highlighting
function handleMouseUp(event: MouseEvent) {
  const selection = window.getSelection();
  
  if (selection && selection.rangeCount > 0 && !selection.isCollapsed) {
    const range = selection.getRangeAt(0);
    const selectedText = range.toString();
    
    // Show highlight options
    showHighlightMenu(event.clientX, event.clientY, range);
  }
}
```

### Note Management
```typescript
// Show note editor
function showNoteEditor(annotationId: string, x: number, y: number) {
  const noteEditor = document.createElement('div');
  noteEditor.className = 'annotation-note';
  noteEditor.style.left = x + 'px';
  noteEditor.style.top = y + 'px';
  
  // Create note input
  const input = document.createElement('textarea');
  const existing = annotateManager.getNote(annotationId);
  input.value = existing || '';
  
  noteEditor.appendChild(input);
  document.body.appendChild(noteEditor);
}
```

## Development Tips

1. **Performance**: Use event delegation for highlight click handling
2. **Mobile Support**: Implement touch event handlers for mobile highlighting
3. **Accessibility**: Add ARIA labels and keyboard navigation
4. **Storage**: Consider localStorage for temporary annotation persistence
5. **Export**: Implement PDF export with preserved annotations
6. **Review Mode**: Create annotation review interface for post-test analysis