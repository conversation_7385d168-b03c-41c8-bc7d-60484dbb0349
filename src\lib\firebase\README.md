# Firebase Integration

Client-side Firebase integration handling authentication, Firestore database operations, and vocabulary data management with Svelte 5 reactivity.

## Overview

This module provides Firebase services for user authentication, real-time database operations, and specialized vocabulary learning data management. It uses Svelte 5 runes for reactive state management and integrates with server-side authentication.

## Architecture

```
firebase/
├── index.ts           # Central exports for all Firebase services
├── config.ts          # Firebase app configuration
├── auth.svelte.ts     # Authentication state management (Svelte 5)  
├── firestore.ts       # Firestore database client
└── vocabDB.ts         # Vocabulary-specific database operations
```

## Core Services

### Authentication System
```typescript
// auth.svelte.ts - Svelte 5 reactive auth state
import { auth, user } from '$lib/firebase';

// Reactive auth state using Svelte stores
$: isAuthenticated = !!$user;
$: userRoles = $user?.customClaims?.roles || [];

// Check user access permissions
$: hasProAccess = userRoles.includes('Pro');
$: hasBootcampAccess = userRoles.includes('Bootcamp');
```

### Database Operations
```typescript
// firestore.ts - Database client
import { db } from '$lib/firebase';
import { doc, getDoc, setDoc, onSnapshot } from 'firebase/firestore';

// Save user progress
const saveUserProgress = async (userId: string, data: any) => {
  await setDoc(doc(db, 'users', userId), data, { merge: true });
};

// Real-time data subscription
const subscribeToUserData = (userId: string, callback: (data: any) => void) => {
  return onSnapshot(doc(db, 'users', userId), (doc) => {
    callback(doc.data());
  });
};
```

### Vocabulary Database
```typescript
// vocabDB.ts - Specialized vocab operations
import { 
  saveVocabProgress,
  getVocabDeck,
  updateCardSchedule,
  getUserVocabStats 
} from '$lib/firebase';

// Save spaced repetition progress
await saveVocabProgress(userId, deckId, cardId, {
  difficulty: 'good',
  scheduledFor: new Date(),
  lastReviewed: new Date()
});
```

## Configuration

### Firebase App Setup
```typescript
// config.ts - Firebase initialization
const firebaseConfig = {
  apiKey: "AIzaSyDTY-WDWMupCgzvT3VzFfgR_w6a1KI1EyI",
  authDomain: "dsat16-17663.firebaseapp.com",
  projectId: "dsat16-17663",
  storageBucket: "dsat16-17663.appspot.com",
  // ... other config
};

export const app = initializeApp(firebaseConfig);
```

**⚠️ Security Note**: Consider moving configuration to environment variables for production:
```typescript
// Recommended for production
const firebaseConfig = {
  apiKey: process.env.PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.PUBLIC_FIREBASE_AUTH_DOMAIN,
  // ... other config from env
};
```

## Authentication Patterns

### User State Management
```svelte
<!-- Component using auth state -->
<script>
  import { user } from '$lib/firebase/auth.svelte';
  import { onMount } from 'svelte';
  
  let userProfile = $state(null);
  
  // Reactive updates when user changes
  $effect(() => {
    if ($user?.uid) {
      loadUserProfile($user.uid);
    }
  });
  
  async function loadUserProfile(userId: string) {
    // Load additional user data from Firestore
    const profile = await getUserProfile(userId);
    userProfile = profile;
  }
</script>

{#if $user}
  <p>Welcome, {$user.displayName || $user.email}!</p>
  
  {#if userProfile}
    <div class="user-stats">
      <p>Current Streak: {userProfile.currentStreak}</p>
      <p>Tests Completed: {userProfile.testsCompleted}</p>
    </div>
  {/if}
{:else}
  <p>Please sign in to continue</p>
{/if}
```

### Role-based Access Control
```typescript
// Check user permissions
import { user } from '$lib/firebase/auth.svelte';
import { get } from 'svelte/store';

export function hasRole(requiredRole: string): boolean {
  const currentUser = get(user);
  const userRoles = currentUser?.customClaims?.roles || [];
  return userRoles.includes(requiredRole);
}

// Usage in components
$: canAccessBootcamp = hasRole('Bootcamp') || hasRole('Pro');
$: canAccessAdvancedFeatures = hasRole('Pro');
```

## Database Patterns

### User Data Structure
```typescript
// Firestore document structure
interface UserDocument {
  // Basic profile
  email: string;
  displayName?: string;
  createdAt: Date;
  lastLogin: Date;
  
  // Subscription info
  subscriptionStatus: 'active' | 'cancelled' | 'trial';
  subscriptionType: 'Pro' | 'Bootcamp' | 'Pro+Bootcamp';
  
  // Study progress
  currentScore: {
    total: number;
    math: number;
    verbal: number;
    lastUpdated: Date;
  };
  
  // Mission progress
  currentStreak: number;
  longestStreak: number;
  totalPoints: number;
  
  // Feature usage
  testsCompleted: number;
  questionsAnswered: number;
  vocabCardsLearned: number;
  studyTimeMinutes: number;
}
```

### Real-time Subscriptions
```typescript
// Reactive data loading pattern
import { onMount } from 'svelte';

let userData = $state(null);
let loading = $state(true);

onMount(() => {
  const unsubscribe = subscribeToUserData($user?.uid, (data) => {
    userData = data;
    loading = false;
  });
  
  return unsubscribe; // Cleanup subscription
});
```

## Vocabulary Integration

### Spaced Repetition System
```typescript
// vocabDB.ts integration with ts-fsrs
import { FSRS } from 'ts-fsrs';

interface VocabCard {
  id: string;
  word: string;
  definition: string;
  example: string;
  
  // Spaced repetition data
  due: Date;
  stability: number;
  difficulty: number;
  elapsed_days: number;
  scheduled_days: number;
  reps: number;
  lapses: number;
  state: 'new' | 'learning' | 'review' | 'relearning';
}

// Update card after review
export async function reviewVocabCard(
  userId: string, 
  deckId: string, 
  cardId: string, 
  rating: 'again' | 'hard' | 'good' | 'easy'
) {
  const fsrs = new FSRS();
  const card = await getVocabCard(userId, deckId, cardId);
  
  // Calculate next review schedule
  const result = fsrs.repeat(card, rating);
  
  // Save updated card
  await updateVocabCard(userId, deckId, cardId, result.card);
}
```

## Server-side Integration

### Session Management
```typescript
// Integration with hooks.server.ts
import { adminAuth } from '$lib/server/admin';

// Verify Firebase ID token server-side
export async function verifySessionToken(idToken: string) {
  try {
    const decodedToken = await adminAuth.verifyIdToken(idToken);
    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      customClaims: decodedToken
    };
  } catch (error) {
    return null;
  }
}
```

### Custom Claims Management
```typescript
// Server-side role management
import { adminAuth } from '$lib/server/admin';

// Update user roles after subscription change
export async function updateUserRoles(userId: string, roles: string[]) {
  await adminAuth.setCustomUserClaims(userId, { roles });
}

// Example: After Stripe webhook
await updateUserRoles(userId, ['Pro']);
```

## Security Rules

### Firestore Security Rules
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Mission progress - user-specific
    match /users/{userId}/missionProgress/{document} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Vocabulary data - user-specific
    match /users/{userId}/vocabDecks/{document} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Shared analysis results - public read if shared
    match /analyses/{analysisId} {
      allow read: if resource.data.shared == true;
      allow write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

## Error Handling

### Authentication Errors
```typescript
// Handle auth errors gracefully
import { auth } from '$lib/firebase';

try {
  await signInWithEmailAndPassword(auth, email, password);
} catch (error) {
  switch (error.code) {
    case 'auth/user-not-found':
      return 'Account not found';
    case 'auth/wrong-password':
      return 'Incorrect password';
    case 'auth/too-many-requests':
      return 'Too many attempts. Try again later';
    default:
      return 'Sign in failed';
  }
}
```

### Offline Support
```typescript
// Enable offline persistence
import { enablePersistence } from 'firebase/firestore';

if (browser) {
  enablePersistence(db).catch((err) => {
    console.warn('Offline persistence failed:', err);
  });
}
```

## Development Tips

1. **Environment Setup**: Use Firebase emulators for local development
2. **Type Safety**: Define interfaces for all Firestore document structures
3. **Performance**: Use Firestore queries efficiently with proper indexing
4. **Security**: Always validate data on server-side with Firebase Functions
5. **Testing**: Mock Firebase services for unit tests
6. **Monitoring**: Set up Firebase Performance Monitoring for production

## Migration Notes

- **Region**: Configured for asia-southeast1 deployment
- **Svelte 5**: Uses new reactivity patterns with `$state` and `$effect`
- **Session Cookies**: Integrates with SvelteKit server-side authentication
- **Custom Claims**: Role-based access control through Firebase custom claims