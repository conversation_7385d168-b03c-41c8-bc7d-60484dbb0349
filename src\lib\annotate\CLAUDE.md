# annotate/

Question annotation system for test simulations allowing students to mark and highlight text.

## Components
- **annotateManager.ts** - Core annotation engine managing highlight/note creation, storage, and retrieval during test sessions

**Purpose**: Enables students to annotate passages and questions during mock tests, similar to paper-based test experience
**Storage**: Annotations likely stored in session state or Firestore for persistence across test sections
**Usage**: Integrated into mock test simulation components for passage-based questions