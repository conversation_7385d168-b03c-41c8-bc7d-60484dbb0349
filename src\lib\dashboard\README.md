# Dashboard System

User dashboard components displaying study progress, achievements, and personalized metrics to motivate continued learning.

## Overview

The dashboard system provides a comprehensive view of user progress through interactive widgets showing mission completion, score estimates, progress tracking, and gamification elements like streaks and achievements.

## Architecture

```
dashboard/
├── DailyMissions.svelte        # Daily mission tracking with progress bars
├── StreakBadge.svelte         # Streak counter with motivational messages
├── EstimatedScore.svelte      # Current SAT score estimation
├── QuestionBankProgress.svelte # Question bank completion tracking
├── VocabProgress.svelte       # Vocabulary learning progress
├── UniAim.svelte              # University target goals
└── ScoreDescription.svelte    # Score interpretation and guidance
```

## Core Components

### Daily Missions Widget
```svelte
<!-- DailyMissions.svelte -->
<script>
  import { DailyMissions } from '$lib/dashboard';
  import { 
    missionProgress, 
    missionCompletionPercentages,
    allMissionsComplete 
  } from '$lib/missions';
  
  // Automatically tracks daily mission progress
  $: isComplete = $allMissionsComplete;
  $: percentages = $missionCompletionPercentages;
</script>

<DailyMissions />

<!-- Shows:
- Daily Questions (5/5 complete) [Progress bar]
- Daily Vocab (1/1 complete) [Progress bar]  
- Morning Study (✓ completed)
- Evening Study (pending)
-->
```

### Streak Tracking
```svelte
<!-- StreakBadge.svelte -->
<script>
  import { StreakBadge } from '$lib/dashboard';
  import { streakData } from '$lib/missions';
  
  $: currentStreak = $streakData?.currentStreak ?? 0;
  $: longestStreak = $streakData?.longestStreak ?? 0;
</script>

<StreakBadge />

<!-- Displays:
- Current streak: "7 days 🔥 On fire!"
- Longest streak: "Personal best: 15 days"
- Color coding based on streak length
-->
```

### Progress Tracking
```svelte
<!-- QuestionBankProgress.svelte -->
<script>
  import { QuestionBankProgress, VocabProgress } from '$lib/dashboard';
</script>

<div class="progress-grid">
  <QuestionBankProgress />  <!-- Shows questions answered by category -->
  <VocabProgress />         <!-- Shows vocabulary cards learned -->
</div>
```

## Data Integration

### Mission System Integration
```typescript
// DailyMissions.svelte integrates with missions
import { 
  missionProgress,           // Current progress for today
  missionCompletionPercentages, // Percentage complete (0-100)
  allMissionsComplete       // Boolean if all complete
} from '$lib/missions';

function getMissionProgress(missionId: string) {
  const current = $missionProgress?.missions[missionId] ?? 0;
  const percentage = $missionCompletionPercentages[missionId] ?? 0;
  return { current, target: mission.target, percentage };
}
```

### Score Estimation
```typescript
// EstimatedScore.svelte shows predicted SAT score
interface ScoreData {
  totalScore: number;        // 400-1600
  mathScore: number;         // 200-800
  verbalScore: number;       // 200-800
  confidence: number;        // Confidence interval
  trend: 'up' | 'down' | 'stable'; // Score trend
}

// Updated after each mock test completion
const updateScoreEstimate = (testResults: TestResults) => {
  // Calculate new estimated score based on performance
  const newEstimate = calculateScoreEstimate(testResults);
  
  // Update user profile with new estimate
  updateUserScore(userId, newEstimate);
};
```

### Progress Metrics
```typescript
// QuestionBankProgress.svelte tracks question practice
interface ProgressData {
  totalQuestions: number;    // Total questions available
  answeredQuestions: number; // Questions user has attempted
  correctAnswers: number;    // Correct answers
  byCategory: {             // Progress by question type
    reading: { total: number; answered: number; correct: number };
    writing: { total: number; answered: number; correct: number };
    math: { total: number; answered: number; correct: number };
  };
}
```

## UI Components Integration

### Progress Visualization
```svelte
<!-- Using progress bars from analysis module -->
<script>
  import ProgressBar from '$lib/analysis/ProgressBar.svelte';
  
  let missions = getDailyMissions();
</script>

{#each missions as mission}
  <div class="mission-card">
    <h4>{mission.name}</h4>
    <p>{mission.description}</p>
    
    <ProgressBar 
      current={getMissionProgress(mission.id).current}
      target={mission.target}
      color="var(--tangerine)"
    />
    
    <button onclick={() => handleMissionClick(mission.id)}>
      {getMissionButtonText(mission.id)}
    </button>
  </div>
{/each}
```

### Streak Visualization
```typescript
// StreakBadge.svelte dynamic messaging
function getStreakMessage(streak: number): string {
  if (streak === 0) return "Start your streak today!";
  if (streak === 1) return "Great start!";
  if (streak < 7) return "Building momentum!";
  if (streak < 30) return "On fire! 🔥";
  if (streak < 100) return "Incredible dedication!";
  return "Legendary streak! 🏆";
}

function getStreakColor(streak: number): string {
  if (streak === 0) return "var(--charcoal)";
  if (streak < 7) return "var(--tangerine)";
  if (streak < 30) return "var(--rose)";
  return "var(--purple)";
}
```

## University Goals System

### Target Setting
```svelte
<!-- UniAim.svelte - University target system -->
<script>
  let targetUniversity = $state("Harvard University");
  let requiredScore = $state(1520);
  let currentScore = $state(1420);
  let gap = $derived(requiredScore - currentScore);
</script>

<div class="uni-aim-card">
  <h3>Target: {targetUniversity}</h3>
  <p>Required Score: {requiredScore}</p>
  <p>Current Estimate: {currentScore}</p>
  
  {#if gap > 0}
    <p class="gap">Gap to close: {gap} points</p>
    <ProgressBar current={currentScore} target={requiredScore} />
  {:else}
    <p class="success">Target achieved! 🎉</p>
  {/if}
</div>
```

## Real-time Updates

### Firebase Integration
```typescript
// Real-time progress updates
import { onSnapshot, doc } from 'firebase/firestore';
import { db } from '$lib/firebase';

// Subscribe to user progress updates
const subscribeToProgress = (userId: string) => {
  return onSnapshot(doc(db, 'users', userId), (doc) => {
    const userData = doc.data();
    
    // Update dashboard metrics
    updateQuestionBankProgress(userData.questionBankProgress);
    updateVocabProgress(userData.vocabProgress);
    updateScoreEstimate(userData.currentScore);
  });
};
```

### PostHog Analytics Integration
```typescript
// Track dashboard engagement
import posthog from 'posthog-js';

// Track when user views dashboard
posthog.capture('dashboard_viewed', {
  currentStreak: $streakData?.currentStreak,
  missionsComplete: $allMissionsComplete,
  estimatedScore: currentScore
});

// Track mission clicks from dashboard
const handleMissionClick = (missionId: string) => {
  posthog.capture('mission_started_from_dashboard', {
    missionId,
    source: 'dashboard'
  });
  
  goto(getMissionRoute(missionId));
};
```

## Responsive Design

### Mobile Optimization
```css
/* Dashboard grid layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.dashboard-card {
  @apply bg-white rounded-lg p-4 shadow-sm border;
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .mission-card {
    @apply p-3 text-sm;
  }
}
```

## Development Patterns

### Component Props
```typescript
// Standard dashboard component interface
interface DashboardComponent {
  userId?: string;           // Optional override for user ID
  loading?: boolean;         // Loading state
  error?: string | null;     // Error message
  refreshInterval?: number;  // Auto-refresh interval (ms)
}
```

### State Management
```svelte
<!-- Example dashboard component pattern -->
<script lang="ts">
  import { onMount } from 'svelte';
  import { user } from '$lib/firebase/auth.svelte';
  
  let loading = $state(true);
  let error = $state<string | null>(null);
  let data = $state(null);
  
  onMount(() => {
    const unsubscribe = subscribeToUserData($user?.uid);
    return unsubscribe;
  });
  
  // Reactive updates based on user changes
  $effect(() => {
    if ($user?.uid) {
      refreshDashboardData($user.uid);
    }
  });
</script>
```

## Development Tips

1. **Performance**: Lazy load dashboard cards for faster initial render
2. **Caching**: Cache progress data in localStorage for offline viewing
3. **Animations**: Use smooth transitions for progress bar updates
4. **Accessibility**: Ensure progress indicators have proper ARIA labels
5. **Testing**: Mock Firebase and mission stores for component testing
6. **Personalization**: Customize dashboard layout based on user preferences