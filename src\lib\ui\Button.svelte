<!-- 
    @component
    A button with box shadow. Can be primary (sky blue) or secondary (white). Can also be a bit bigger than the default size (button1 in figma).
    
    Usage:
    ```tsx
    <Button onclick={function}>Click me!</Button>
    ```
-->

<script lang="ts">
    import type { Snippet } from 'svelte';
	import type { Mouse<PERSON>ventHandler } from 'svelte/elements';

    interface Props {
        onclick?: MouseEventHandler<HTMLButtonElement>;
        isSecondary?: boolean;
        fullWidth?: boolean;
        disabled?: boolean;
        type?: 'button' | 'submit' | 'reset';
        children?: Snippet;
        title?: string
    }

    let {
        onclick = () => {},
        isSecondary = false,
        fullWidth = false,
        disabled = false,
        type = 'button',
        children,
        title
    }: Props = $props();
</script>

<button class:secondary={isSecondary} class:fullWidth onclick={onclick} {disabled} {type} {title}>
    {@render children?.()}
</button>

<style>
    button {
        padding: 0.75rem 1.5rem;
        border: 1px solid var(--pitch-black);
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem 0 var(--box-shadow-color, var(--pitch-black));
        background: var(--button-bg-color, var(--sky-blue));
        width: var(--width, fit-content);

        display: inline-flex;
        gap: 10px;
        align-items: center;
        justify-content: center;

        font-family: "Open Sans";
        font-weight: 600;
        font-size: 18px;
        color: var(--button-text-color, var(--pitch-black));
    }

    button:disabled {
        background: #EEEEEE;
        cursor: not-allowed;
    }

    .secondary {
        background: #FFFFFF;
    }

    button:active:enabled {
        box-shadow: none;
        transform: translate(0.25rem, 0.25rem);
    }

    .fullWidth {
        width: 100%;
    }

    @media (max-width: 768px) {
        button {
            font-size: 1rem;
        }
    }
</style>